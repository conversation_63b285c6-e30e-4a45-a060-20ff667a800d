/* eslint-disable no-nested-ternary */
/* eslint-disable prefer-const */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useEffect, useMemo, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Modal, ModalHeader, ModalBody } from "reactstrap";
import AddIcon from "@material-ui/icons/Add";
import Select from "react-select";

import {
  MuiPickersUtilsProvider,
  DateTimePicker as MaterialDateTimePicker,
} from "@material-ui/pickers";
import moment from "moment";
import PaymentDropDown from "components/DropDowns/PaymentDropDown";
import { RecallPaymentGateway } from "gql/queries/RecallPaymentGateway.gql";
import MomentUtils from "@date-io/moment";
import { Button, TextField } from "@material-ui/core";
import { useLazyQuery, useMutation, useQuery } from "@apollo/client";
import { RentalExtensionRequestPrice } from "gql/queries/Rental.queries.gql";
import { ConfirmRentalDateExtensionRequest } from "gql/mutations/ConfirmExtension.gql";
import { ResendRentalExtensionIntegration } from "gql/mutations/ResendExtensionToAlly.gql";
import {
  CreateRentalDateExtensionRequest,
  UpdateRentalDateExtensionRequest,
  RejectRentalDateExtensionRequest,
} from "gql/mutations/Rental.mutations.gql";
import { Cancel, Check, Close, Edit, Refresh } from "@material-ui/icons";
import SendIcon from "@material-ui/icons/Send";
import { NotificationManager } from "react-notifications";
import PaymentStatusDropDown from "components/DropDowns/PaymentStatusDropDown";
import { MonthesExtension } from "constants/constants";
import { userCan } from "functions";
import store from "../../../../store";
import RefundExtensionComponent from "./refundExtension";
import PaymentLink from "../components/PaymentLink";
export default function ExtenstionRequests({
  extensionModalOpen,
  setIsExtensionModalOpen,
  rentalDateExtensionRequests,
  rentalDetails,
  refetchBooking,
  company,
}) {
  const { formatMessage, locale } = useIntl();
  const [requests, setRequests] = useState(rentalDetails?.rentalDateExtensionRequests || []);
  const [confirmedRequests, setConfirmedRequests] = useState(
    requests?.filter((request) => request.status === "confirmed"),
  );
  const [newDropOffDate, setNewDropOffDate] = useState(null);
  const [newDropOffTime, setNewDropOffTime] = useState(null);
  const [showAddBtn, setShowAddBtn] = useState(true);
  const [currentRequestId, setCurrentRequestId] = useState(null);
  const [loader, setLoader] = useState(false);
  const [fixedPrice, setFixedPrice] = useState();
  const [updatedFixedPrice, setUpdatedFixedPrice] = useState();
  // Get user data from store first
  const { ally_id, is_super_user, agency_id } = store.getState()?.authUser.user;

  const [Value, setValue] = useState(
    rentalDateExtensionRequests?.find((rent) => rent.status == "pending")?.totalRemainingPrice,
  );
  const [confrimExtension] = useMutation(ConfirmRentalDateExtensionRequest);
  const [paymentMethod, setPaymentMethod] = useState(
    agency_id
      ? "ONLINE" // Agency users can only use ONLINE payment method
      : rentalDateExtensionRequests?.find((rent) => rent.status == "pending")?.paymentMethod ||
          rentalDetails?.paymentMethod,
  );
  const [paymentStatus, setPaymentStatus] = useState(
    rentalDateExtensionRequests?.find((rent) => rent.status == "pending")?.isPaid
      ? "paid"
      : "not_paid",
  );
  // const { data: rentalExtensionRequestPriceData, error: rentalExtensionRequestPriceErr } = useQuery(
  //   RentalExtensionRequestPrice,
  //   {
  //     skip: !rentalDetails?.id || newDropOffDate == null || newDropOffTime == null ,
  //     variables: {
  //       rentalId: rentalDetails?.id,
  //       usedPrice:updatedFixedPrice,

  //       dropOffDate: moment(newDropOffDate).locale("en-US").format("DD/MM/YYYY") || undefined,
  //       dropOffTime:
  //         moment(newDropOffTime, "hh:mm:ss A").locale("en-US").format("HH:mm:ss") || undefined,

  //     },
  //     errorPolicy: "all",
  //     onError(error) {
  //       NotificationManager.error(error.message);
  //     },
  //   },
  // );
  const [
    getRentaExtensionPrice,
    { data: rentalExtensionRequestPriceData, error: rentalExtensionRequestPriceErr },
  ] = useLazyQuery(RentalExtensionRequestPrice, {
    fetchPolicy: "no-cache",
  });
  const { refetch: _recallPaymentGateway } = useQuery(RecallPaymentGateway, { skip: true });
  const [discount, setDiscount] = useState();
  const [valueChanged, setValueChanged] = useState();

  const errorExtesntionRentalPriceMemo = useMemo(
    () => rentalExtensionRequestPriceErr,
    [rentalExtensionRequestPriceErr?.message],
  );
  const [months, setMonths] = useState("1");
  const [createRentalDateExtensionRequest] = useMutation(CreateRentalDateExtensionRequest);
  const [updateRentalDateExtensionRequest] = useMutation(UpdateRentalDateExtensionRequest);
  const [rejectRentalDateExtensionRequest] = useMutation(RejectRentalDateExtensionRequest);
  const [resendRentalExtensionIntegration] = useMutation(ResendRentalExtensionIntegration);
  useMemo(() => {
    setRequests(
      rentalDateExtensionRequests?.map((item) => ({
        canSendExtensionToAlly: item.canSendExtensionToAlly,
        createdAt: item.createdAt,
        dropOffDate: item.dropOffDate,
        dropOffTime: item.dropOffTime,
        extensionDays: item.extensionDays,
        id: item.id,
        isPaid: item.isPaid,
        paymentBrand: item.paymentBrand,
        paymentMethod: item.actualPaymentMethod,
        paymentStatus: item.paymentStatus,
        refundable: item.refundable,
        refundedAt: item.refundedAt,
        requestNo: item.requestNo,
        status: item.status,
        statusLocalized: item.statusLocalized,
        totalRemainingPrice: item.totalRemainingPrice,
        totalWalletPaidAmount: item.totalWalletPaidAmount,
        walletTransactions: item.walletTransactions,
        withWallet: item.withWallet,
        usedPrice: item.pricePerDay,
        discount: item.discount,
        originalPricePerDay: item.originalPricePerDay,
        packagePricePerDay: item.packagePricePerDay,
        payable: item.payable,
        paymentLink: item.paymentLink,
        paymentLinkToken: item.paymentLink?.token,
        paymentLinkValidForPayment: item.paymentLink?.validForPayment,
      })),
    );
  }, [rentalDateExtensionRequests]);
  const ConfirmExtension = (id) => {
    confrimExtension({
      variables: {
        rentalExtensionId: id,
      },
    })
      .then(() => {
        NotificationManager.success(
          <FormattedMessage id="Extension request  is confirmed successfully" />,
        );
        refetchBooking();
        setShowAddBtn(true);
      })
      .catch((err) => {
        NotificationManager.error(err.message);
      });
  };
  useEffect(() => {
    refetchBooking();
  }, []);
  useEffect(() => {
    moment.locale(locale === "ar" ? "ar" : "en-au");
  }, [locale]);

  function addBtnHanlder() {
    setShowAddBtn(true);
    setRequests([{}, ...requests]);
  }

  function dropOffDateTimeHandler(date, id) {
    const filteredItem = { ...requests.find((i) => i.id === id) };
    const requestWithoutFilterItem = requests.filter((i) => i.id !== id);

    if (date === null) {
      // Handle date clearing
      filteredItem.dropOffDate = null;
      filteredItem.dropOffTime = null;
      setNewDropOffDate(null);
      setNewDropOffTime(null);
    } else {
      filteredItem.dropOffDate = moment(date).locale("en-US").format("YYYY-MM-DD");
      filteredItem.dropOffTime = moment(date).locale("en-US").format("HH:mm:ss");
      setNewDropOffDate(filteredItem.dropOffDate);
      setNewDropOffTime(filteredItem.dropOffTime);
    }

    setCurrentRequestId(filteredItem.id);
    setRequests([filteredItem, ...requestWithoutFilterItem]);
  }
  function handelDate(date, id) {
    const filteredItem = { ...requests.find((i) => i.id === id) };
    const requestWithoutFilterItem = requests.filter((i) => i.id !== id);

    if (date === null) {
      // Handle date clearing
      filteredItem.dropOffDate = null;
      filteredItem.dropOffTime = null;
      setNewDropOffDate(null);
      setNewDropOffTime(null);
    } else {
      filteredItem.dropOffDate = moment(date).locale("en-US").format("YYYY-MM-DD");
      filteredItem.dropOffTime = rentalDetails.dropOffTime;
      setNewDropOffDate(filteredItem.dropOffDate);
      setNewDropOffTime(filteredItem.dropOffTime);
    }

    setCurrentRequestId(filteredItem.id);
    setRequests([filteredItem, ...requestWithoutFilterItem]);
  }

  function addNewRequestHandler(request) {
    const data = {
      rentalId: rentalDetails?.id,
      dropOffDate: moment(newDropOffDate).locale("en-US").format("DD/MM/YYYY"),
      dropOffTime: moment(newDropOffTime, "hh:mm:ss A").locale("en-US").format("HH:mm:ss"),
      paymentMethod: rentalDetails.installments?.length
        ? "ONLINE"
        : agency_id
        ? "ONLINE"
        : paymentMethod,
      usedPrice: rentalDetails.installments?.length ? undefined : +Number(fixedPrice).toFixed(2),
      withInstallment: !!rentalDetails.installments?.length,
      isPaid: paymentMethod != "CASH" ? undefined : paymentStatus != "not_paid",
    };
    async function mutation() {
      try {
        setLoader(true);
        const res = await createRentalDateExtensionRequest({ variables: data });
        if (res.data && res.data.createRentalDateExtensionRequest.status === "success") {
          NotificationManager.success(formatMessage({ id: "success.create.extensionRequest" }));
          setLoader(false);
          refetchBooking();

          // setIsExtensionModalOpen(false);
        }
      } catch (e) {
        setLoader(false);
        NotificationManager.error(e.message);
      }
    }
    if (data && Object.keys(data).length >= 4) {
      mutation();
    }
  }
  function updateRequestHandler(request) {
    const data = {
      id: currentRequestId,
      dropOffDate: newDropOffDate
        ? moment(newDropOffDate).locale("en-US").format("DD/MM/YYYY")
        : moment(request.dropOffDate).locale("en-US").format("DD/MM/YYYY"),
      dropOffTime: newDropOffTime
        ? moment(newDropOffTime, "hh:mm:ss A").locale("en-US").format("HH:mm:ss")
        : moment(request.dropOffTime, "hh:mm:ss A").locale("en-US").format("HH:mm:ss"),
      usedPrice: rentalDetails.installments.length
        ? undefined
        : Number(parseFloat(request.usedPrice).toFixed(2)),
      isPaid: paymentMethod != "CASH" ? undefined : paymentStatus != "not_paid",
      withInstallment: !!rentalDetails.installments?.length,
    };
    async function mutation() {
      try {
        const res = await updateRentalDateExtensionRequest({ variables: data });
        if (res.data && res.data.updateRentalDateExtensionRequest.status === "success") {
          NotificationManager.success(formatMessage({ id: "success.update.extensionRequest" }));
          refetchBooking();
          setCurrentRequestId(null);
        }
      } catch (e) {
        NotificationManager.error(e.message);
      }
    }
    if (data && Object.keys(data).length) {
      mutation();
    }
  }

  function rejectRequestHandler(id) {
    rejectRentalDateExtensionRequest({ variables: { rentalExtensionId: id } })
      .then((res) => {
        if (res.data.rejectRentalDateExtensionRequest.status === "success") {
          NotificationManager.success(
            <FormattedMessage id="Extension request is rejected successfully" />,
          );
          refetchBooking();
          setShowAddBtn(true);
        } else {
          NotificationManager.error(<FormattedMessage id=" Error " />);
        }
      })
      .catch((err) => NotificationManager.error(err.message));
  }
  function sendToAlly(id) {
    resendRentalExtensionIntegration({
      variables: {
        rentalExtensionId: id,
      },
    })
      .then(() => {
        NotificationManager.success(<FormattedMessage id="Successfully sent to the ally" />);
        refetchBooking();
      })
      .catch(() =>
        NotificationManager.error(<FormattedMessage id="Unable to send request to ally" />),
      );
  }
  useEffect(() => {
    if (rentalExtensionRequestPriceData) {
      setValue(rentalExtensionRequestPriceData.rentalExtensionRequestPrice.totalRemainingPrice);
      setFixedPrice(rentalExtensionRequestPriceData.rentalExtensionRequestPrice?.pricePerDay);
      setDiscount(rentalExtensionRequestPriceData.rentalExtensionRequestPrice.discount);
    }
  }, [rentalExtensionRequestPriceData]);

  useEffect(() => {
    if (newDropOffDate) {
      getRentaExtensionPrice({
        variables: {
          rentalId: rentalDetails?.id,
          usedPrice: updatedFixedPrice,

          dropOffDate: moment(newDropOffDate).locale("en-US").format("DD/MM/YYYY") || undefined,
          dropOffTime:
            moment(newDropOffTime, "hh:mm:ss A").locale("en-US").format("HH:mm:ss") || undefined,
        },
      }).then((res) => {
        if (res?.errors?.length) {
          NotificationManager.error(res?.errors?.[0].message);
        }
      });
    }
  }, [newDropOffDate, newDropOffTime, updatedFixedPrice]);
  useEffect(() => {
    setConfirmedRequests(
      rentalDetails?.rentalDateExtensionRequests?.filter(
        (request) => request.status == "confirmed",
      ),
    );
  }, [rentalDetails]);
  const ReCalculatePrice = () => {
    getRentaExtensionPrice({
      variables: {
        rentalId: rentalDetails?.id,
        usedPrice: Number(parseFloat(fixedPrice).toFixed(2)),

        dropOffDate: moment(newDropOffDate).locale("en-US").format("DD/MM/YYYY") || undefined,
        dropOffTime:
          moment(newDropOffTime, "hh:mm:ss A").locale("en-US").format("HH:mm:ss") || undefined,
      },
    }).then(() => {
      setValueChanged(false);
    });
  };
  return (
    <Modal isOpen={extensionModalOpen} style={{ maxWidth: "fit-content" }}>
      <>
        <div className="d-flex justify-content-between align-items-center px-2">
          <Close style={{ cursor: "pointer" }} onClick={() => setIsExtensionModalOpen(false)} />
          <ModalHeader>
            <h2 style={{ fontSize: "24px" }}>
              <FormattedMessage id="Extension Requests" />
            </h2>
          </ModalHeader>
          {!rentalDetails?.hasPendingExtensionRequests &&
          rentalDetails?.status === "car_received" ? (
            <div
              className="d-flex align-items-center"
              style={{ cursor: "pointer" }}
              onClick={addBtnHanlder}
            >
              {showAddBtn && (
                <Button variant="outlined">
                  <span>
                    <FormattedMessage id="button.add" />
                  </span>
                  <AddIcon />
                </Button>
              )}
            </div>
          ) : (
            <div />
          )}
        </div>

        {requests?.length ? (
          <ModalBody>
            <table style={{ borderCollapse: "collapse" }}>
              <tr className="px-2 py-2">
                <th scope="col" className="px-2 py-2 table-bordered" aria-label="Request No.">
                  <FormattedMessage id="Request No." />
                </th>
                <th
                  scope="col"
                  className="px-2 py-2 table-bordered"
                  style={{ width: "220px" }}
                  aria-label="New return Time"
                >
                  <FormattedMessage id="New_return_Time" />
                </th>
                <th scope="col" className="px-2 py-2 table-bordered" aria-label="Extension Days">
                  <FormattedMessage id="Extension Days" />
                </th>
                <th
                  scope="col"
                  className="px-2 py-2 table-bordered"
                  style={{ width: "100px" }}
                  aria-label="Due Value"
                >
                  <FormattedMessage id="Due Value" />
                </th>
                {rentalDetails.installments?.length ? null : (
                  <th
                    scope="col"
                    className="px-2 py-2 table-bordered"
                    style={{ width: "100px" }}
                    aria-label="Original Price per Day"
                  >
                    <FormattedMessage id="Original Price/ Day" />
                  </th>
                )}
                {rentalDetails.installments?.length ? null : (
                  <th
                    scope="col"
                    className="px-2 py-2 table-bordered"
                    style={{ width: "100px" }}
                    aria-label="Price per Day"
                  >
                    <FormattedMessage id="Price/ Day" />
                  </th>
                )}
                {rentalDetails.installments?.length ? null : (
                  <th
                    scope="col"
                    className="px-2 py-2 table-bordered"
                    style={{ width: "100px" }}
                    aria-label="Recommended Price per Day"
                  >
                    <FormattedMessage id="Recommended Price/Day" />
                  </th>
                )}
                {rentalDetails.installments?.length ? null : (
                  <th
                    scope="col"
                    className="px-2 py-2 table-bordered"
                    style={{ width: "100px" }}
                    aria-label="Discount Rate"
                  >
                    <FormattedMessage id="Discount Rate" />
                  </th>
                )}
                {rentalDetails.installments?.length ? null : (
                  <th
                    scope="col"
                    className="px-2 py-2 table-bordered"
                    style={{ width: "100px" }}
                    aria-label="Payment Method"
                  >
                    <FormattedMessage id="paymentmethod" />
                  </th>
                )}
                {rentalDetails.installments?.length ? null : (
                  <th
                    scope="col"
                    className="px-2 py-2 table-bordered"
                    style={{ width: "100px" }}
                    aria-label="Payment Brand"
                  >
                    <FormattedMessage id="Payment.Brand" />
                  </th>
                )}

                <th scope="col" className="px-2 py-2 table-bordered" aria-label="Request Status">
                  <FormattedMessage id="Request status" />
                </th>
                <th
                  scope="col"
                  className="px-2 py-2 table-bordered"
                  style={{ minWidth: "140px" }}
                  aria-label="Payment Status"
                >
                  <FormattedMessage id="Payment status" />
                </th>
                <th scope="col" className="px-2 py-2 table-bordered" aria-label="Actions">
                  <FormattedMessage id="common.actions" />
                </th>
              </tr>

              {requests?.map((i, index) => (
                <tr key={index}>
                  <td className="px-2 py-2 table-bordered">{`${i?.requestNo || "-"}`}</td>
                  <td className="px-2 py-2 table-bordered">
                    {rentalDetails.installments?.length ? (
                      <Select
                        placeholder={formatMessage({ id: "months.count" })}
                        options={MonthesExtension}
                        isDisabled={i.status !== "pending" && i?.status}
                        value={MonthesExtension.find(
                          (month) =>
                            +month.value === +i.months || +month.value === i.extensionDays / 30,
                        )}
                        onChange={(selection) => {
                          if (rentalDetails?.lastConfirmedExtensionRequest) {
                            const date = moment(
                              rentalDetails?.lastConfirmedExtensionRequest?.dropOffDate,
                            )
                              .locale("en")
                              .add(+selection.value * 30, "days");
                            handelDate(date, i.id);
                            const AllRequests = [...requests];
                            AllRequests[index].months = +selection.value;
                            setRequests(AllRequests);
                          } else {
                            const date = moment(rentalDetails?.dropOffDate)
                              .locale("en")
                              .add(+selection.value * 30, "days");
                            handelDate(date, i.id);
                            const AllRequests = [...requests];
                            AllRequests[index].months = +selection.value;
                            setRequests(AllRequests);
                          }
                        }}
                        getOptionLabel={(options) => <FormattedMessage id={options.label} />}
                      ></Select>
                    ) : (
                      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                        <div style={{ flex: 1 }}>
                          <MuiPickersUtilsProvider
                            libInstance={moment}
                            utils={MomentUtils}
                            locale={locale}
                          >
                            <MaterialDateTimePicker
                              okLabel={formatMessage({ id: "ok" })}
                              cancelLabel={formatMessage({ id: "cancel" })}
                              style={{ width: "100%" }}
                              value={
                                i?.dropOffDate && i?.dropOffTime
                                  ? moment(
                                      `${i?.dropOffDate} ${i.dropOffTime}`,
                                      "YYYY-MM-DD HH:mm:ss",
                                    )
                                  : null
                              }
                              onChange={(date) => {
                                dropOffDateTimeHandler(date, i?.id);
                              }}
                              name="dob"
                              minDate={
                                rentalDetails?.status !== "car_received" ||
                                (i?.status ? i?.status !== "pending" : null)
                                  ? moment(
                                      `${i?.dropOffDate} ${i.dropOffTime}`,
                                      "YYYY-MM-DD HH:mm:ss",
                                    )
                                  : moment(rentalDetails.dropOffDate).add(1, "days")
                              }
                              placeholder={formatMessage({ id: "drop_off_date" })}
                              renderInput={(props) => <TextField {...props} />}
                              format="DD-MM-YYYY hh:mm:ss A"
                              ampm
                              disabled={
                                rentalDetails?.status !== "car_received" ||
                                (i?.status ? i?.status !== "pending" : null)
                              }
                            />
                          </MuiPickersUtilsProvider>
                        </div>
                        {i?.dropOffDate &&
                          i?.dropOffTime &&
                          rentalDetails?.status === "car_received" &&
                          (!i?.status || i?.status === "pending") && (
                            <Button
                              size="small"
                              variant="outlined"
                              color="secondary"
                              onClick={() => dropOffDateTimeHandler(null, i?.id)}
                              style={{ minWidth: "auto", padding: "6px 8px" }}
                            >
                              <Close fontSize="small" />
                            </Button>
                          )}
                      </div>
                    )}
                  </td>
                  <td className="px-2 py-2 table-bordered">{`${
                    i?.id === currentRequestId &&
                    rentalExtensionRequestPriceData?.rentalExtensionRequestPrice?.extensionDays
                      ? rentalExtensionRequestPriceData?.rentalExtensionRequestPrice
                          ?.extensionDays || "-"
                      : i?.extensionDays || "-"
                  } ${
                    i?.extensionDays ||
                    rentalExtensionRequestPriceData?.rentalExtensionRequestPrice?.extensionDays
                      ? formatMessage({
                          id: "day",
                        })
                      : ""
                  }`}</td>
                  <td className="px-2 py-2 table-bordered">
                    {`${
                      i?.id === currentRequestId
                        ? rentalExtensionRequestPriceData?.rentalExtensionRequestPrice
                            ?.totalRemainingPrice ||
                          Value ||
                          "-"
                        : i?.totalRemainingPrice || "-"
                    }`}
                  </td>
                  {rentalDetails.installments?.length ||
                  rentalDetails.mergedInstallments?.length ? null : (
                    <td className="px-2 py-2 table-bordered">
                      {`${
                        i?.id === currentRequestId
                          ? rentalExtensionRequestPriceData?.rentalExtensionRequestPrice
                              ?.originalPricePerDay || "-"
                          : i?.originalPricePerDay || "-"
                      }`}
                    </td>
                  )}
                  {rentalDetails.installments?.length ||
                  rentalDetails.mergedInstallments?.length ? null : (
                    <td className="px-2 py-2 table-bordered">
                      {`${
                        i?.id === currentRequestId
                          ? rentalExtensionRequestPriceData?.rentalExtensionRequestPrice
                              ?.packagePricePerDay || "-"
                          : i?.packagePricePerDay || "-"
                      }`}
                    </td>
                  )}
                  {rentalDetails.installments?.length ||
                  rentalDetails.mergedInstallments?.length ? null : (
                    <td className="px-2 py-2 table-bordered">
                      {((i?.status?.toLowerCase() === "pending" || !i?.status) &&
                        (i?.paymentMethod?.toLowerCase() === "cash" || !i?.paymentMethod)) ||
                      (i?.status === "pending" && i?.paymentMethod === "ONLINE" && !i?.isPaid) ? (
                        agency_id ? (
                          // Agency users cannot edit prices - show read-only value
                          `${
                            !i.requestNo
                              ? fixedPrice
                              : i.id === currentRequestId
                              ? fixedPrice
                              : i?.usedPrice || "-"
                          }`
                        ) : (
                          <TextField
                            InputProps={{ inputProps: { maxLength: 9 } }}
                            // defaultValue={i?.totalRemainingPrice}
                            // value={rentalExtensionRequestPriceData?.rentalExtensionRequestPrice?.pricePerDay}
                            value={
                              !i.requestNo
                                ? fixedPrice
                                : i.id === currentRequestId
                                ? fixedPrice
                                : i?.usedPrice
                            }
                            onChange={(e) => {
                              const alllrequests = [...requests];
                              if (Number.isInteger(Number(e.target.value))) {
                                setFixedPrice(e.target.value);
                                alllrequests[index].usedPrice = e.target.value;
                                setCurrentRequestId(i.id);

                                setRequests(alllrequests);
                              } else {
                                setCurrentRequestId(i.id);
                                alllrequests[index].usedPrice = Number.parseFloat(e.target.value);
                                setFixedPrice(Number.parseFloat(e.target.value));

                                setRequests(alllrequests);
                              }
                              setValueChanged(true);
                            }}
                          />
                        )
                      ) : (
                        `${
                          i?.id === currentRequestId
                            ? rentalExtensionRequestPriceData?.rentalExtensionRequestPrice
                                ?.pricePerDay ||
                              Value ||
                              "-"
                            : i?.usedPrice || "-"
                        }`
                      )}
                    </td>
                  )}

                  {rentalDetails.installments.length ||
                  rentalDetails.mergedInstallments.length ? null : (
                    <td className="px-2 py-2 table-bordered">
                      {i?.id === currentRequestId ? discount : i.discount}
                    </td>
                  )}

                  {rentalDetails.installments.length ||
                  rentalDetails.mergedInstallments.length ? null : (
                    <td className="px-2 py-2 table-bordered" style={{ minWidth: "150px" }}>
                      {(i?.status === "pending" || !i.status) &&
                      !i?.requestNo &&
                      rentalDetails.paymentMethod !== "CASH" ? (
                        agency_id ? (
                          // Agency users can only use ONLINE payment method - show read-only
                          `${formatMessage({ id: "online" })}`
                        ) : (
                          <PaymentDropDown
                            valueAttribute="id"
                            withoutAll
                            selectedPayment={paymentMethod}
                            setSelectedPayment={(payment) => {
                              setCurrentRequestId(i.id);

                              setValue(
                                i?.requestNo
                                  ? i?.totalRemainingPrice
                                  : rentalExtensionRequestPriceData?.rentalExtensionRequestPrice
                                      ?.totalRemainingPrice,
                              );
                              setPaymentMethod(payment);
                            }}
                          />
                        )
                      ) : (
                        `${formatMessage({ id: i?.paymentMethod || rentalDetails.paymentMethod })}`
                      )}
                    </td>
                  )}

                  {rentalDetails?.mergedInstallments?.length ||
                  rentalDetails.installments?.length ? null : (
                    <td className="px-2 py-2 table-bordered">{i?.paymentBrand}</td>
                  )}
                  <td className="px-2 py-2 table-bordered">{`${
                    i?.statusLocalized || formatMessage({ id: "PENDING" })
                  }`}</td>

                  <td className="px-2 py-2 table-bordered">
                    {(i?.status === "pending" || !i?.status) &&
                    paymentMethod === "CASH" &&
                    is_super_user &&
                    !ally_id &&
                    !agency_id ? (
                      <PaymentStatusDropDown
                        valueAttribute="id"
                        selectedPaymentStatus={paymentStatus}
                        inExtension
                        setSelectedPaymentStatus={(paymentStatusFilter) => {
                          setCurrentRequestId(i.id);
                          setPaymentStatus(paymentStatusFilter);
                        }}
                      />
                    ) : i?.refundedAt && i.isPaid ? (
                      <FormattedMessage id="refund.money" />
                    ) : i.isPaid ? (
                      <FormattedMessage id="Paid" />
                    ) : (
                      <div
                        style={!ally_id ? { cursor: "pointer", textDecoration: "underline" } : {}}
                        onClick={() => {
                          if (
                            !ally_id &&
                            (i?.paymentStatus === "failed" ||
                              i?.paymentStatus === "pending" ||
                              i?.paymentStatus === "pending_transaction")
                          ) {
                            _recallPaymentGateway({
                              rentalId: rentalDetails.id,
                              rentalDateExtensionRequestId: i.id,
                            }).finally(() => {
                              refetchBooking();
                            });
                          }
                        }}
                      >
                        <div
                          title={
                            !ally_id &&
                            (i?.paymentStatus === "failed" ||
                              i?.paymentStatus === "pending" ||
                              i?.paymentStatus === "pending_transaction")
                              ? formatMessage({ id: "RecallGateway" })
                              : ""
                          }
                          style={{
                            display: "flex",
                            alignItems: "baseline",
                            gap: "4px",
                            cursor: "pointer",
                          }}
                        >
                          {i?.paymentStatus === "failed" ||
                          i?.paymentStatus === "pending" ||
                          i?.paymentStatus === "pending_transaction" ? (
                            <i className="fa fa-refresh" />
                          ) : null}
                          <p className="m-0">
                            {i?.paymentStatus === "pending_transaction" ||
                            i?.paymentStatus === "pending" ? (
                              <FormattedMessage id="pending" />
                            ) : (
                              formatMessage({ id: "notpayed" })
                            )}
                          </p>
                        </div>
                      </div>
                    )}
                  </td>
                  <td className="px-2 py-2 table-bordered">
                    {i.refundable && userCan("rentals.refund") ? (
                      <RefundExtensionComponent
                        rentalid={i.id}
                        extensionDetails={i}
                        refetch={refetchBooking}
                      />
                    ) : null}
                    {i?.requestNo ? (
                      i?.status === "pending" && rentalDetails?.status === "car_received" ? (
                        agency_id ? (
                          // Agency users cannot perform status change actions
                          <span style={{ color: "#666", fontStyle: "italic" }}>
                            {formatMessage({ id: "pending" })}
                          </span>
                        ) : (
                          <div className="d-flex" style={{ gap: "4px" }}>
                            {i?.id === currentRequestId && (
                              <div onClick={() => updateRequestHandler(i)}>
                                <label title={formatMessage({ id: "Edit" })}>
                                  <Edit style={{ cursor: "pointer" }} />
                                </label>
                              </div>
                            )}
                            <label title={formatMessage({ id: "common.confirm" })}>
                              <Check
                                disabled
                                style={{ cursor: "pointer" }}
                                onClick={() => ConfirmExtension(i.id)}
                              />
                            </label>
                            <label title={formatMessage({ id: "button.reject" })}>
                              <Cancel
                                onClick={() => rejectRequestHandler(i.id)}
                                style={{ cursor: "pointer" }}
                              />
                            </label>
                          </div>
                        )
                      ) : i.canSendExtensionToAlly && !ally_id && !agency_id ? (
                        <label title={formatMessage({ id: "Resend to ally" })}>
                          <SendIcon
                            onClick={() => sendToAlly(i.id)}
                            style={{ cursor: "pointer" }}
                          />
                        </label>
                      ) : null
                    ) : valueChanged ? (
                      <Button
                        disabled={!newDropOffDate || !newDropOffTime || loader}
                        variant="outlined"
                        color="primary"
                        onClick={() => ReCalculatePrice()}
                      >
                        <FormattedMessage id="Recalculate" />
                      </Button>
                    ) : (
                      <Button
                        disabled={!newDropOffDate || !newDropOffTime || loader}
                        variant="outlined"
                        color="primary"
                        onClick={() => addNewRequestHandler(i)}
                      >
                        <FormattedMessage id="AddRequest" />
                      </Button>
                    )}
                    {i?.payable && (
                      <PaymentLink
                        payableId={i.id}
                        payableType="RentalDateExtensionRequest"
                        hide={!i?.payable}
                        token={i.paymentLink?.token}
                        validForPayment={i.paymentLink?.validForPayment}
                      />
                    )}
                  </td>
                </tr>
              ))}
            </table>
          </ModalBody>
        ) : (
          <p className="text-center my-4">
            <FormattedMessage id="No data found" />
          </p>
        )}
      </>
    </Modal>
  );
}
