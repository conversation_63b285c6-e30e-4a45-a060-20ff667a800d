/* eslint-disable react/jsx-no-bind */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useState, useEffect } from "react";
import { useLocation, useParams, useHistory } from "react-router-dom";
import { Helmet } from "react-helmet";
import { FormattedMessage, useIntl } from "react-intl";
import { EditBooking } from "gql/mutations/Rental.mutations.gql";
import { EditSuggestedPrice } from "gql/mutations/UpdateSuggestedPrice.gql";
import IntlMessages from "util/IntlMessages";
import { withStyles } from "@material-ui/core/styles";
import PageTitleBar from "components/PageTitleBar/PageTitleBar";
import {
  GetRentalDetailsQuery,
  GetRentPrice,
  RentalAboutPrice,
} from "gql/queries/Rental.queries.gql";
import { GetCustomerDetailsQuery } from "gql/queries/Users.queries.gql";
import { GetAllyCompanyQuery } from "gql/queries/Ally.queries.gql";
import { GetCarProfile } from "gql/queries/Cars.queries.gql";
import { useMutation, useQuery } from "@apollo/client";
import InfoCard from "components/shared/InfoCard";
import GoogleMapComponent from "routes/maps/google-map";
import DotsLoader from "components/shared/DotsLoader";
import { CustomerDataDisplay } from "components/CustomerDataDisplay";
import moment from "moment";
import { daysDifference, userCan } from "functions";
import { DateTimePickerCustom } from "components/DateTimePickerCustom";
import { NotificationManager } from "react-notifications";
import Button from "@material-ui/core/Button";
import { PrintRental } from "gql/mutations/PrintRental.gql";
import print from "print-js";
import { CircularProgress, Tooltip } from "@material-ui/core";
import { Branch } from "gql/queries/getBranchDetails.gql";
import { ResendRentalExtensionIntegration } from "gql/mutations/ResendExtensionToAlly.gql";
import { AllAreas } from "gql/queries/Areas.queries.gql";
import { Profile } from "gql/queries/AdminProfile.gql";
import { UserWallet } from "gql/queries/CustomerWalletBalance.gql";
import { AgencyDtails } from "gql/queries/AgencyDetails.gql";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import DeleteIcon from "@material-ui/icons/Delete";
import Accordion from "@material-ui/core/Accordion";
import AccordionSummary from "@material-ui/core/AccordionSummary";
import Menu from "@material-ui/core/Menu";
import MenuItem from "@material-ui/core/MenuItem";
import ListItemIcon from "@material-ui/core/ListItemIcon";
import ListItemText from "@material-ui/core/ListItemText";
import DraftsIcon from "@material-ui/icons/Drafts";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import FilterListIcon from "@material-ui/icons/FilterList";
import ExpandLessIcon from "@material-ui/icons/ExpandLess";
import Typography from "@material-ui/core/Typography";
import RiyalComponent from "components/shared/RiyalComponent";
import store from "../../../../store";
import ExtenstionRequests from "../AddEditBooking/extensionRequests";
import InstallmentsTable from "../AddEditBooking/InstallmentsTable";
import ChangeStatusModal from "./ChangeStatusModal";
import AddNoteModal from "./AddNoteModal";
import UpdateSuggestedPrice from "./UpdateSuggestedPricModal";
import ChangeDuration from "./ChangeDuration";
import UpdateExtraService from "./UpdateExtraServiceModal";
import PlansTable from "../AddEditBooking/PlansTable";
import BookingPriceSummary from "./BookingPriceSummary";
import TimeLine from "../components/TimeLineModal";
import BookingInstallmentPriceSummary from "./BookingInstallmentPriceSummary";
import useAssignBooking from "../hooks/useAssignBooking";
import UsersModal from "../components/UsersModal";
import RefundBooking from "../components/RefundBooking";

/**
 * @name BookingDetails
 * @description Booking Details Or Rental Details.This function takes bookingIf from params
 *              and graps booking data Then takes carId, userId & allyCompanyId to get each
 *              details and display needed data inside InfoCards
 * @export
 * @return {JSX}
 */
const StyledMenu = withStyles({
  paper: {
    border: "1px solid #d3d4d5",
  },
})((props) => (
  <Menu
    elevation={0}
    getContentAnchorEl={null}
    anchorOrigin={{
      vertical: "bottom",
      horizontal: "center",
    }}
    transformOrigin={{
      vertical: "top",
      horizontal: "center",
    }}
    {...props}
  />
));

const StyledMenuItem = withStyles((theme) => ({
  root: {
    "&:focus": {
      backgroundColor: theme.palette.primary.main,
      "& .MuiListItemIcon-root, & .MuiListItemText-primary": {
        color: theme.palette.common.white,
      },
    },
  },
}))(MenuItem);
export default function BookingDetails() {
  const location = useLocation();
  const history = useHistory();
  const { locale, formatMessage } = useIntl();
  const { bookingId } = useParams();
  const [bookingDetails, setBookingDetails] = useState([]);
  const [carDetails, setCarDetails] = useState([]);
  const [allyDetails, setAllyDetails] = useState([]);
  const [latLng, setLatLng] = useState(null);
  const [newDropOffDate, setNewDropOffDate] = useState();
  const [changed, setChanged] = useState(false);
  const [branch, setBranch] = useState();
  const [branchId, setBranchID] = useState(null);
  const [progress, setProgress] = useState(false);
  const [EditBookingMutation, { loading: editingRental }] = useMutation(EditBooking);
  const [editSuggestedPrice] = useMutation(EditSuggestedPrice);
  const [extensionModalOpen, setIsExtensionModalOpen] = useState(false);
  const [openNoteModal, setopenNoteModal] = useState();
  // Rental Details Request
  const {
    data: rentalDetails,
    loading,
    refetch,
  } = useQuery(GetRentalDetailsQuery, {
    variables: { id: bookingId },
  });
  const { data: branchDetails } = useQuery(Branch, {
    skip: !branchId,
    variables: {
      id: branchId,
    },
  });

  const [resendRentalExtensionIntegration] = useMutation(ResendRentalExtensionIntegration);
  const [openPriceModal, setOpenPriceModal] = useState();
  const [openDuration, setOpenDuration] = useState();
  const [openextraService, setOpenExtraService] = useState();
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const { data: userInfo } = useQuery(Profile);

  // Agency Data Request
  const user = store.getState()?.authUser?.user;
  const { data: agency } = useQuery(AgencyDtails, {
    skip: !user?.agency_id,
    variables: { id: +user?.agency_id },
  });

  // Ally Data Request
  const { data: allyDetailsRes } = useQuery(GetAllyCompanyQuery, {
    skip: !rentalDetails?.rentalDetails?.allyCompanyId,
    variables: { id: rentalDetails?.rentalDetails?.allyCompanyId },
  });
  const [printrental] = useMutation(PrintRental);
  const [bookingtotals, setBookingTotals] = useState([]);
  const [opneTimeLineModal, setOpenTimeLineModal] = useState(false);

  // Customer Data Request
  const { data: customerDetailsRes } = useQuery(GetCustomerDetailsQuery, {
    skip: !rentalDetails?.rentalDetails,
    variables: { id: rentalDetails?.rentalDetails?.userId },
  });
  const { data: walletBalance } = useQuery(UserWallet, {
    skip: !rentalDetails?.rentalDetails,
    variables: { userId: +rentalDetails?.rentalDetails?.userId },
  });

  // Car Details Request
  const { data: carDetailsRes } = useQuery(GetCarProfile, {
    skip: !rentalDetails?.rentalDetails,
    variables: { id: rentalDetails?.rentalDetails?.carId },
  });
  const { data: AreasRes, loading: gettingAreas } = useQuery(AllAreas);
  const { ally_id } = store.getState()?.authUser.user;

  const { data: BookingInstallmentSumary } = useQuery(RentalAboutPrice, {
    skip: !rentalDetails?.rentalDetails?.installments?.length,
    errorPolicy: "all",
    onError(error) {
      NotificationManager.error(error.message);
    },
    variables: {
      id: rentalDetails?.rentalDetails?.id,
      withInstallment: true,
    },
  });
  const {
    data: BookingPriceRes,
    loading: calculatingPrice,
    refetch: recalculateRentPrice,
  } = useQuery(GetRentPrice, {
    skip: true,
    errorPolicy: "all",
    onError(error) {
      NotificationManager.error(error.message);
    },
    variables: {
      carId: rentalDetails?.rentalDetails.carId,
      isUnlimited: rentalDetails?.rentalDetails.isUnlimited,
      deliveryPrice: rentalDetails?.rentalDetails.deliveryPrice,
      handoverPrice: rentalDetails?.rentalDetails?.handoverPrice,
      handoverBranch:
        rentalDetails?.rentalDetails?.branchId !== rentalDetails?.rentalDetails?.dropOffBranchId
          ? rentalDetails?.rentalDetails?.dropOffBranchId
          : null,
      deliverLat:
        rentalDetails?.rentalDetails?.deliverLat ||
        AreasRes?.areas.find((area) => +area?.id === +rentalDetails?.rentalDetails?.pickUpCityId)
          ?.centerLat,
      deliverLng:
        rentalDetails?.rentalDetails?.deliverLng ||
        AreasRes?.areas.find((area) => +area?.id === +rentalDetails?.rentalDetails?.pickUpCityId)
          ?.centerLng,
      couponId: rentalDetails?.rentalDetails?.couponId
        ? +rentalDetails?.rentalDetails?.copounId
        : null,
      deliveryType: rentalDetails?.rentalDetails?.deliverType,
      dropOffDate: rentalDetails?.rentalDetails?.dropOffDate
        ? moment(rentalDetails?.rentalDetails?.dropOffDate).format("DD/MM/YYYY")
        : null,
      dropOffTime: rentalDetails?.rentalDetails?.dropOffTime,
      insuranceId: rentalDetails?.rentalDetails?.insuranceId
        ? +rentalDetails?.rentalDetails?.insuranceId
        : null,
      ownCarPlanId: rentalDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan?.id,
      pickUpDate: moment(rentalDetails?.rentalDetails?.pickUpDate).format("DD/MM/YYYY"),
      pickUpTime: rentalDetails?.rentalDetails?.pickUpTime,
      allyExtraServices: rentalDetails?.rentalDetails?.rentalExtraServices
        ?.filter((s) => s.extraServiceType === "ally_company")
        .map((item) => +item?.id),
      branchExtraServices: rentalDetails?.rentalDetails?.rentalExtraServices
        ?.filter((s) => s.extraServiceType === "branch")
        .map((i) => i.id),
      usedPrice: rentalDetails?.rentalDetails?.pricePerDay
        ? rentalDetails?.rentalDetails?.pricePerDay
        : null,
      // withWallet: rentalDetails?.rentalDetails?.walletTransactions,
      walletPaidAmount: rentalDetails?.rentalDetails?.walletTransactions?.amount
        ? rentalDetails?.rentalDetails?.walletTransactions?.amount
        : null,
      suggestedPrice: rentalDetails?.rentalDetails?.suggestedPrice,
      rentalId: rentalDetails?.rentalDetails?.id,
    },
  });
  const getRentalAudits = () => {
    setOpenTimeLineModal(true);
  };
  const {
    AssignBooking,
    BookingRecord,
    OpenUsersModal,
    setOpenUsersModal,
    AssignBookingBySuperUser,
    customerCare,
  } = useAssignBooking({
    refetchBooking: refetch,
    inBookingDetails: true,
  });
  const getStatusColor = (status) => {
    const statusLower = status.toLowerCase();
    if (statusLower === "pending") return "#9EA6A9";
    if (statusLower === "invoiced") return "#27AE60";
    if (statusLower === "confirmed") return "#7AB3C5";
    if (statusLower === "cancelled" || statusLower === "closed") return "#F85959";
    return "#FA9C3F";
  };
  useEffect(() => {
    // if (rentalDetails?.rentalDetails) {
    const rentInfo = rentalDetails?.rentalDetails;
    const pickupBranchId = +rentInfo?.branchId;
    const branch = allyDetailsRes?.allyCompany?.branches?.find(
      (branch) => +branch?.id === +pickupBranchId,
    );

    const branchName = branch?.[`${locale}Name`];
    setBranchID(rentInfo?.branchId);

    const bookingType = () => {
      const { deliverLng, pickUpDate, dropOffDate, isRentToOwn } = rentInfo;
      switch (true) {
        case isRentToOwn:
          return "rent-to-own";
        case !!deliverLng:
          return "delivery";
        case Math.abs(moment(pickUpDate).diff(moment(dropOffDate), "days")) >= 25:
          return "monthly";
        default:
          return "daily";
      }
    };

    if (rentInfo?.id) {
      const bookingDetails = [
        {
          msgId: "rental.bookingStatus",
          value: <IntlMessages id={rentInfo.status.toUpperCase()} />,
          color: getStatusColor(rentInfo.status),
        },
        { msgId: "rental.paymentType", value: rentInfo.paymentMethod },

        {
          msgId: "rental.bookingSubStatus",
          value: rentInfo?.subStatusLocalized,
        },

        { msgId: "rental.bookingNumber", value: rentInfo.id },
        { msgId: "bookingNo.placeholder", value: rentInfo.bookingNo },
        { msgId: "rental.pickupLocation", value: rentInfo[`${locale}PickUpCityName`] },
        { msgId: "rental.pickupBranchName", value: branchName || "" },
        { msgId: "rental.returnLocation", value: rentInfo[`${locale}DropOffCityName`] },
        { msgId: "rental.returnBranchName", value: branchName || "" },
        {
          msgId: "Pickup date and time",
          value: `${rentInfo?.pickUpDate} ${rentInfo?.pickUpTime}`,
        },
        {
          msgId: "Return date and time",
          value: rentInfo?.dropOffDate ? `${rentInfo?.dropOffDate} ${rentInfo?.dropOffTime}` : null,
        },
        {
          msgId: "Total rental days",
          value: `${rentInfo?.numberOfDays}`,
        },
        {
          msgId: "Total insurance amount",
          value: rentInfo?.totalInsurancePrice,
        },
        {
          msgId: "Price before tax",
          value: rentInfo?.priceBeforeTax,
        },
        {
          msgId: "Tax",
          value: rentInfo?.taxValue,
        },
        { msgId: "Grand Total", value: rentInfo?.totalBookingPrice },
        { msgId: "Paid Amount", value: rentInfo?.paidAmount },

        rentInfo.isIntegratedRental && {
          msgId: "rental.IntegrationStatus",
          value: rentInfo.rentalIntegrationStatus || "",
        },
        rentInfo.isIntegratedRental && {
          msgId: "rental.cancelReason",
          value: rentInfo.rentalIntegrationErrorMessage || "",
        },
        rentInfo.isUnlimited && {
          msgId: "Unlimited.KM",
          value: rentInfo.unlimitedFeePerDay,
        },
        {
          msgId: "code.label",
          value: rentInfo.couponCode,
          // couponDiscount
        },
        {
          msgId: "couponDiscount",
          value: (
            <div
              style={{
                display: "flex",
                gap: "3px",
                alignItems: "center",
                height: "100%",
                direction: "ltr",
              }}
            >
              <RiyalComponent />
              <span>{rentInfo.couponDiscount}</span>
            </div>
          ),

          // couponDiscount
        },

        { msgId: "rental.priceDay", value: rentInfo.pricePerDay },
        { msgId: "rental.bookingType", value: <IntlMessages id={bookingType()} /> },

        {
          msgId: "paymentstatus",
          value: (() => {
            if (rentInfo?.withInstallment || rentInfo?.isRentToOwn) {
              return (
                <>
                  {rentInfo?.installments?.find((i) => i?.hasPendingPaymentTransaction) ? (
                    <FormattedMessage id="pending" />
                  ) : (
                    <>
                      <FormattedMessage id="rent.payed" />
                      {rentInfo?.paidInstallmentsCount}
                    </>
                  )}
                </>
              );
            }

            if (rentInfo?.hasPendingPaymentTransaction) {
              return <FormattedMessage id="pending" />;
            }

            return rentInfo.isPaid ? (
              <FormattedMessage id="payed" />
            ) : (
              <FormattedMessage id="notpayed" />
            );
          })(),
        },
        rentInfo.refundedAt && {
          msgId: "rental.refundamount",
          value: rentInfo.refundedAmount ? rentInfo.refundedAmount : 0,
        },
        rentInfo.refundedAt && {
          msgId: "rental.refundedAt",
          value: moment.utc(rentInfo.refundedAt).local().format("DD/MM/YYYY HH:mm:ss"),
        },
        {
          msgId: "booking.invociedAt",
          value:
            rentInfo?.invoicedAt &&
            moment.utc(rentInfo?.invoicedAt).local().format("DD/MM/YYYY HH:mm:ss"),
        },

        rentInfo?.walletTransactions
          ? {
              msgId: "wallet",
              value: rentInfo.walletTransactions.amount,
            }
          : null,

        rentInfo?.walletTransactions
          ? { msgId: "Due Amount", value: rentInfo.totalAmountDue }
          : null,
        rentInfo.isPaid && rentInfo.paymentMethod === "ONLINE"
          ? {
              msgId: "rental.paymentBrand",
              value: rentInfo?.withInstallment
                ? null
                : rentInfo?.rentalPayments.find((rent) => rent.status === "paid")?.paymentBrand,
            }
          : null,
        { msgId: "note", value: rentInfo.note },
      ];
      if (rentInfo?.rentalExtraServices) {
        for (const item of rentInfo.rentalExtraServices) {
          bookingDetails.push({
            msgId: item.title,
            value: item.subtitle,
          });
        }
      }

      setBookingDetails(bookingDetails);
    }
  }, [rentalDetails, allyDetailsRes, carDetailsRes]);

  useEffect(() => {
    if (carDetailsRes?.carProfile) {
      const carData = carDetailsRes?.carProfile;
      const rentInfo = rentalDetails?.rentalDetails;
      const carName = `${carData?.carMakeName} ${carData?.carModelName} ${carData?.carVersionName}`;
      const insurance = carData?.carInsurances?.find(
        (insurance) => +insurance?.id === +rentalDetails?.rentalDetails?.insuranceId,
      );
      if (carData?.ownCarDetail) {
        const carDetails = [
          { msgId: "carName", value: carName },
          { msgId: "rental.distanceByDay", value: carData?.distanceByDay },
          { msgId: "acrissCode", value: carData.carModel.acrissCode },
          { msgId: "rental.distanceBetweenCarUser", value: carData?.distanceBetweenCarUser },
          { msgId: "insuranceType", value: insurance?.insuranceName },
          {
            msgId: "rental.insuranceCostPerDay",
            value: (rentInfo?.totalInsurancePrice / rentInfo?.numberOfDays).toFixed(2),
          },
          {
            msgId: "status",
            value: <FormattedMessage id={carData?.availabilityStatus ? "active" : "inactive"} />,
          },
          { msgId: "rental.carListingIdOrNumber", value: rentInfo?.carId },
          { msgId: "car.color", value: carData?.ownCarDetail?.color?.name },
          {
            msgId: "car.PlateNumber",
            value:
              locale === "en" ? carData?.ownCarDetail?.plateNoEn : carData?.ownCarDetail?.plateNoAr,
          },
          {
            msgId: "car.status",
            value: carData?.ownCarDetail?.isNewCar ? (
              <FormattedMessage id="New" />
            ) : (
              <FormattedMessage id="Used" />
            ),
          },
          { msgId: "car.km", value: carData?.ownCarDetail?.km },
        ];
        setCarDetails(carDetails);
      } else {
        const carDetails = [
          { msgId: "carName", value: carName },
          { msgId: "acrissCode", value: carData.carModel.acrissCode },
          { msgId: "rental.distanceByDay", value: carData?.distanceByDay },
          { msgId: "rental.distanceBetweenCarUser", value: carData?.distanceBetweenCarUser },
          { msgId: "insuranceType", value: insurance?.insuranceName },
          {
            msgId: "rental.insuranceCostPerDay",
            value: (rentInfo?.totalInsurancePrice / rentInfo?.numberOfDays).toFixed(2),
          },
          {
            msgId: "status",
            value: <FormattedMessage id={carData?.availabilityStatus ? "active" : "inactive"} />,
          },
          { msgId: "rental.carListingIdOrNumber", value: rentInfo?.carId },
          { msgId: "rental.rentPerDay", value: rentInfo?.pricePerDay },
          { msgId: "rental.monthlyPrice", value: carData?.monthlyPrice },
          { msgId: "rental.weeklyPrice", value: carData?.weeklyPrice },
        ];
        setCarDetails(carDetails);
      }
    }
  }, [carDetailsRes]);
  useEffect(() => {
    if (branchDetails) {
      setLatLng([branchDetails.branch?.lat, branchDetails.branch?.lng]);
      setBranch(branchDetails.branch);
    }
  }, [branchDetails]);
  useEffect(() => {
    if (allyDetailsRes?.allyCompany) {
      const allyInfo = allyDetailsRes?.allyCompany;
      const { email, officeNumber, phoneNumber, commisionRate } = allyInfo;
      const allyDetails = [
        { msgId: "rental.allyName", value: allyInfo[`${locale}Name`] },
        { msgId: "rental.allyEmailAddress", value: email },
        { msgId: "rental.allyPhoneNumber", value: <div dir="ltr">{phoneNumber}</div> },
        { msgId: "rental.allyOfficeNumber", value: <div dir="ltr">{officeNumber}</div> },
      ];

      setAllyDetails(allyDetails);

      setBookingDetails((oldBookingDetails) => [
        ...oldBookingDetails,
        {
          msgId: "rental.carwahCommission",
          value: commisionRate,
        },
      ]);
    }
  }, [allyDetailsRes]);
  const Print = () => {
    setProgress(true);
    printrental({
      variables: {
        rentalId: bookingId,
      },
    }).then((data) => {
      print({ printable: data.data.printRental.fileBase64, type: "pdf", base64: true });
      setProgress(false);
    });
  };
  const { status, pickUpDate, dropOffDate } = rentalDetails?.rentalDetails || {};
  useEffect(() => {
    if (rentalDetails?.rentalDetails) {
      const BookingTotals = [
        {
          msgId: "Pickup date and time",
          value: `${rentalDetails?.rentalDetails?.pickUpDate} ${rentalDetails?.rentalDetails?.pickUpTime}`,
        },
        rentalDetails?.rentalDetails?.isRentToOwn
          ? null
          : {
              msgId: "Return date and time",
              value: `${rentalDetails?.rentalDetails?.dropOffDate} ${rentalDetails?.rentalDetails?.dropOffTime}`,
            },
        { msgId: "Total rental days", value: rentalDetails?.rentalDetails?.numberOfDays },
        {
          msgId: "Total insurance amount",
          value: rentalDetails?.rentalDetails?.totalInsurancePrice,
        },
        {
          msgId: "Price before tax",
          value: rentalDetails?.rentalDetails?.priceBeforeTax,
        },
        {
          msgId: "Tax",
          value: rentalDetails?.rentalDetails?.taxValue,
        },
        { msgId: "Grand Total", value: rentalDetails?.rentalDetails?.totalBookingPrice },
        { msgId: "Paid Amount", value: rentalDetails?.rentalDetails.paidAmount },

        rentalDetails?.rentalDetails.loyaltyType
          ? {
              msgId: "Loyalty Partner",
              value: <FormattedMessage id={rentalDetails?.rentalDetails.loyaltyType} />,
            }
          : null,
        { msgId: "Membership ID", value: rentalDetails?.rentalDetails.loyaltyMembership },
        {
          msgId: "Earning Value",
          value: rentalDetails?.rentalDetails.loyaltyCollections?.[0]?.numberOfPoints,
        },
        rentalDetails?.rentalDetails.loyaltyCollections?.[0]?.status
          ? {
              msgId: "Earn Status",
              value: (
                <FormattedMessage
                  id={rentalDetails?.rentalDetails.loyaltyCollections?.[0]?.status}
                />
              ),
            }
          : null,
        {
          msgId: "Miles Price",
          value: rentalDetails?.rentalDetails.loyaltyCollections?.[0]?.pointPrice,
        },
        { msgId: "1 Mile Rate", value: rentalDetails?.rentalDetails.loyaltyPointRate },
        { msgId: "Mile Price", value: rentalDetails?.rentalDetails.loyaltyPointPrice },
        {
          msgId: "Applied Value",
          value: (() => {
            const { loyaltyAppliedValueType, loyaltyMaxAppliedValue, loyaltyAppliedValue } =
              rentalDetails?.rentalDetails;

            if (loyaltyAppliedValueType === "percentage" && loyaltyMaxAppliedValue) {
              return `${loyaltyAppliedValue}%(${loyaltyMaxAppliedValue} ${
                locale === "ar" ? "ريال" : "SAR"
              })`;
            }

            if (loyaltyAppliedValueType === "percentage") {
              return `${loyaltyAppliedValue}%`;
            }

            return loyaltyAppliedValue;
          })(),
        },
      ];
      setBookingTotals(BookingTotals);
    }
  }, [rentalDetails?.rentalDetails]);
  function sendToAlly(id) {
    resendRentalExtensionIntegration({
      variables: {
        rentalExtensionId: id,
      },
    })
      .then(() => {
        NotificationManager.success(<FormattedMessage id="Successfully sent to the ally" />);
        refetch();
      })
      .catch(() => {
        NotificationManager.error(<FormattedMessage id="Unable to send request to ally" />);
      });
  }
  return (
    <div className="ecom-dashboard-wrapper">
      <Helmet>
        <title>Booking Details</title>
        <meta name="description" content="Carwah Booking Details" />
      </Helmet>
      <PageTitleBar
        title={<IntlMessages id="sidebar.rentalDetails" />}
        enableBreadCrumb
        match={location}
        lastElement={rentalDetails?.rentalDetails?.bookingNo || <DotsLoader />}
        extraButtons={
          <div className="d-flex flex-wrap" style={{ gap: "5px" }}>
            {rentalDetails?.rentalDetails && (
              <RefundBooking
                {...{ record: rentalDetails.rentalDetails, refetch }}
                inBookingDetails
              />
            )}

            {userCan("rentals.update_status") &&
              bookingId &&
              (!user?.user_type?.includes("agency") ||
                (user?.user_type?.includes("agency") && !rentalDetails?.rentalDetails?.isPaid)) && (
                <Tooltip title={formatMessage({ id: "change.status" })}>
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                    onClick={() => setOpenStatusModal(true)}
                    style={{ minWidth: "auto", padding: "8px 12px" }}
                  >
                    <FormattedMessage id="change.status" />
                  </Button>
                </Tooltip>
              )}

            {(rentalDetails?.rentalDetails?.status === "pending" ||
              rentalDetails?.rentalDetails?.status === "confirmed") &&
              rentalDetails?.rentalDetails?.subStatus !== "ally_declined" &&
              !rentalDetails?.rentalDetails?.installments?.length &&
              (!user?.user_type?.includes("agency") ||
                (user?.user_type?.includes("agency") && !rentalDetails?.rentalDetails?.isPaid)) && (
                <Tooltip title={formatMessage({ id: "Edit" })}>
                  <Button
                    variant="contained"
                    size="small"
                    onClick={() => history.push(`/cw/dashboard/bookings/${bookingId}/edit`)}
                    style={{ minWidth: "auto", padding: "8px 12px" }}
                    className="btn-warning"
                  >
                    <FormattedMessage id="Edit" />
                  </Button>
                </Tooltip>
              )}

            {Boolean(
              (rentalDetails?.rentalDetails?.status !== "pending" &&
                rentalDetails?.rentalDetails?.status !== "confirmed" &&
                !rentalDetails?.rentalDetails?.installments?.length) ||
                rentalDetails?.rentalDetails?.installments?.length,
            ) && (
              <Tooltip title={formatMessage({ id: "Add Note" })}>
                <Button
                  variant="contained"
                  size="small"
                  onClick={() => setopenNoteModal(true)}
                  style={{ minWidth: "auto", padding: "8px 12px" }}
                  className="btn-default"
                >
                  <FormattedMessage id="Add Note" />
                </Button>
              </Tooltip>
            )}

            {rentalDetails?.rentalDetails?.status !== "pending" &&
              rentalDetails?.rentalDetails?.status !== "confirmed" &&
              !rentalDetails?.rentalDetails?.installments?.length &&
              (!user?.user_type?.includes("agency") ||
                (user?.user_type?.includes("agency") && !rentalDetails?.rentalDetails?.isPaid)) && (
                <Tooltip title={formatMessage({ id: "Update Extra Service" })}>
                  <Button
                    variant="contained"
                    size="small"
                    onClick={() => setOpenExtraService(true)}
                    style={{ minWidth: "auto", padding: "8px 12px" }}
                    className="btn-success"
                  >
                    <FormattedMessage id="Update Extra Service" />
                  </Button>
                </Tooltip>
              )}

            {rentalDetails?.rentalDetails?.status !== "pending" &&
              rentalDetails?.rentalDetails?.status !== "confirmed" &&
              !rentalDetails?.rentalDetails?.installments?.length &&
              !ally_id &&
              (!user?.user_type?.includes("agency") ||
                (user?.user_type?.includes("agency") && !rentalDetails?.rentalDetails?.isPaid)) && (
                <Tooltip title={formatMessage({ id: "change Duration" })}>
                  <Button
                    variant="contained"
                    color="secondary"
                    size="small"
                    onClick={() => setOpenDuration(true)}
                    style={{ minWidth: "auto", padding: "8px 12px" }}
                  >
                    <FormattedMessage id="change Duration" />
                  </Button>
                </Tooltip>
              )}

            {rentalDetails?.rentalDetails?.status !== "pending" &&
              rentalDetails?.rentalDetails?.status !== "confirmed" &&
              !rentalDetails?.rentalDetails?.installments?.length &&
              !user?.user_type?.includes("agency") && (
                <Tooltip title={formatMessage({ id: "update price" })}>
                  <Button
                    variant="contained"
                    size="small"
                    onClick={() => setOpenPriceModal(true)}
                    style={{ minWidth: "auto", padding: "8px 12px" }}
                    className="btn-danger"
                  >
                    <FormattedMessage id="update price" />
                  </Button>
                </Tooltip>
              )}

            {(userCan("rentals.print") || agency?.agency?.isActive) && (
              <Tooltip title={formatMessage({ id: "print" })}>
                <Button
                  variant="contained"
                  size="small"
                  disabled={progress}
                  onClick={Print}
                  style={{ minWidth: "auto", padding: "8px 12px" }}
                  className="btn-info"
                >
                  <FormattedMessage id="print" />
                </Button>
              </Tooltip>
            )}

            {((userCan("rentals.timeline") && !userInfo?.profile?.allyProfile?.allyId) ||
              agency?.agency?.isActive) && (
              <Tooltip title={formatMessage({ id: "common.timeline" })}>
                <Button
                  variant="contained"
                  size="small"
                  onClick={getRentalAudits}
                  style={{ minWidth: "auto", padding: "8px 12px" }}
                  className="btn-timeline"
                >
                  <FormattedMessage id="common.timeline" />
                </Button>
              </Tooltip>
            )}

            {userCan("rentals.extend") &&
              rentalDetails?.rentalDetails?.status &&
              rentalDetails.rentalDetails.status !== "pending" &&
              rentalDetails.rentalDetails.status !== "confirmed" && (
                <Tooltip title={formatMessage({ id: "Extension Requests" })}>
                  <Button
                    variant="contained"
                    color="default"
                    size="small"
                    onClick={() => setIsExtensionModalOpen(true)}
                    style={{ minWidth: "auto", padding: "8px 12px" }}
                  >
                    <FormattedMessage id="Extension Requests" />
                  </Button>
                </Tooltip>
              )}

            <div style={{ minWidth: "auto" }}>{AssignBooking(rentalDetails?.rentalDetails)}</div>
          </div>
        }
      />
      <div>
        {/* {userCan("rentals.update_status") && bookingId && (
           <Tooltip title={formatMessage({ id: "changeStatus" })} placement="top">
           <Button
             style={{ color: "#fff" }}
             className="mx-sm-15 btn btn-info mb-2 mt-2"
             onClick={() => setOpenStatusModal(true)}
           >
             <FormattedMessage id="changeStatus" />
           </Button>
         </Tooltip>
          <ChangeStatus
            inBookingDetails
            rentalDetails={rentalDetails}
            rentalDateExtensionRequests={rentalDetails?.rentalDetails?.rentalDateExtensionRequests}
          />
        )} */}
      </div>
      {location?.pathname?.includes("extend") && !loading && (
        <>
          {bookingId && status === "car_received" ? (
            <div className="d-flex justify-content-end align-items-center">
              <DateTimePickerCustom
                autoOk
                disablePast={!bookingId}
                value={newDropOffDate}
                label={formatMessage({ id: "rental.dropoffDateTime" })}
                onChange={(val) => {
                  setChanged(true);
                  setTimeout(() => {
                    setNewDropOffDate(val);
                  }, 100);
                }}
                minDate={bookingDetails.dropOffDate}
                minDateMessage={formatMessage({ id: "validation.fromMustBeLessThanTo" })}
              />
              <button
                type="button"
                className="btn btn-primary"
                disabled={!changed || editingRental}
                onClick={() => {
                  EditBookingMutation({
                    variables: {
                      ...rentalDetails.rentalDetails,
                      pickUpDate: moment(rentalDetails.rentalDetails.pickUpDate).format(
                        "DD/MM/YYYY",
                      ),
                      dropOffDate: moment(newDropOffDate).format("DD/MM/YYYY"),
                      dropOffTime: `${moment(newDropOffDate).format("HH:mm")}:00`,
                      rentalId: +bookingId,
                      dropOffBranchId: rentalDetails?.rentalDetails?.branchId,
                    },
                  })
                    .then(() => {
                      NotificationManager.success(formatMessage({ id: "success.edit.rental" }));
                      refetch();
                    })
                    .catch((err) => {
                      NotificationManager.error(err.message);
                    });
                }}
              >
                <FormattedMessage id="extend" />
              </button>
            </div>
          ) : (
            <h2 className="badge badge-info" onClick={() => history.goBack()}>
              <FormattedMessage id="this.booking.can't.be.extended" />
            </h2>
          )}
        </>
      )}
      <div className="row mt-4">
        <div className="col-md-6 col-12">
          <InfoCard fullwidth data={allyDetails} titleId="rental.allyDetails" inbookingDetails />

          {latLng?.length === 2 && (
            <div className="booking-details-card">
              <GoogleMapComponent
                branchMap
                heading={formatMessage({ id: "branch.details" })}
                branchHeading={
                  rentalDetails?.rentalDetails?.deliverLng
                    ? formatMessage({
                        id: "Point A is the Pickup Location, Point B is the Delivery Location",
                      })
                    : ""
                }
                lat={latLng[0]}
                lng={latLng[1]}
                lat2={rentalDetails?.rentalDetails?.deliverLat}
                lng2={rentalDetails?.rentalDetails?.deliverLng}
                branch={branch}
                isDelivery={rentalDetails?.rentalDetails?.deliverLat}
              />
            </div>
          )}
          <InfoCard
            fullwidth
            data={carDetails}
            plans={rentalDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan}
            titleId="rental.carDetails"
            inbookingDetails
          />
          <div className="booking-details-card">
            <CustomerDataDisplay customerDetailsRes={customerDetailsRes} refetch={refetch} />
          </div>
        </div>
        <div className="col-md-6 col-12">
          {Boolean(bookingDetails) && (
            <InfoCard
              fullwidth
              data={bookingDetails}
              titleId="rental.main.bookingDetails"
              inbookingDetails
              cancelledReason={rentalDetails?.rentalDetails?.cancelledReason}
              closingReasons={rentalDetails?.rentalDetails.closingReason}
            />
          )}
          {rentalDetails?.rentalDetails ? (
            <InfoCard fullwidth data={bookingtotals} titleId="Booking Totals" inbookingDetails />
          ) : null}

          {rentalDetails?.rentalDetails?.allyRentalRejections?.length
            ? rentalDetails?.rentalDetails?.allyRentalRejections?.map((rejectReason, index) => (
                <InfoCard
                  key={`ally-rejection-${rejectReason.id || index}`}
                  fullwidth
                  // rejectReasones={basket.rejectedReasons}
                  data={[
                    { msgId: "rental.allyName", value: rejectReason[`${locale}AllyName`] },
                    { msgId: "branchName", value: rejectReason[`${locale}BranchName`] },
                    // { msgId: "Decline Reason", value: rejectReason?.rejectedReasons[0]?.body },
                    { msgId: "User name", value: rejectReason?.rejectedByName },
                    {
                      msgId: "Date & Time",
                      value: moment(rejectReason?.createdAt).format("DD/mm/yyyy HH:mm A"),
                    },
                  ]}
                  AllyrejectReason={rejectReason?.rejectedReasons}
                  titleId="Ally Declined"
                  inbookingDetails
                />
              ))
            : null}
          {rentalDetails?.rentalDetails?.rentalRejectedBaskets.length
            ? rentalDetails?.rentalDetails?.rentalRejectedBaskets.map((basket, index) => (
                <InfoCard
                  key={`rejected-basket-${basket.id || index}`}
                  fullwidth
                  rejectReasones={basket.rejectedReasons}
                  data={[{ msgId: "rental.allyName", value: basket[`${locale}AllyName`] }]}
                  titleId="Ally.Decline.reason"
                  inbookingDetails
                />
              ))
            : null}

          {rentalDetails?.rentalDetails?.rentalDateExtensionRequests?.length ? (
            <div className="w-100">
              {rentalDetails.rentalDetails.rentalDateExtensionRequests.map((i, index) => {
                const getPaymentStatus = () => {
                  if (i?.refundedAt && i.isPaid) {
                    return <FormattedMessage id="refund.money" />;
                  }
                  if (i.isPaid) {
                    return <FormattedMessage id="payed" />;
                  }
                  return <FormattedMessage id="notpayed" />;
                };

                return (
                  <InfoCard
                    key={`extension-request-${i.id || index}`}
                    fullwidth
                    data={[
                      { msgId: "Request status", value: i?.status },
                      { msgId: "Request No.", value: i?.requestNo },
                      {
                        msgId: "New_return_Time",
                        value:
                          i?.dropOffDate && i?.dropOffTime
                            ? `${i?.dropOffDate} ${i.dropOffTime}`
                            : null,
                      },
                      {
                        msgId: "Extension duration",
                        value: `${i?.extensionDays} ${formatMessage({
                          id: "day",
                        })}`,
                      },
                      rentalDetails?.rentalDetails?.mergedInstallments?.length ||
                      rentalDetails?.rentalDetails?.installments?.length
                        ? null
                        : {
                            msgId: "paymentmethod",
                            value: formatMessage({
                              id: i?.paymentMethod,
                            }),
                          },
                      {
                        msgId: "paymentstatus",
                        value: getPaymentStatus(),
                      },
                      { msgId: "rental.paymentBrand", value: i?.paymentBrand },
                      { msgId: "Grand Total +VAT", value: i?.totalRemainingPrice },
                      { msgId: "Wallet Amount", value: i?.totalWalletPaidAmount },
                    ]}
                    titleId="Extension Request"
                    ExtensionCard
                    canSendToAlly={i.canSendExtensionToAlly}
                    ExtensionId={i.id}
                    sendToAlly={sendToAlly}
                    inbookingDetails
                  />
                );
              })}
            </div>
          ) : null}
          <div className="booking-details-card mt-4">
            <RctCollapsibleCard
              colClasses="col-sm-12 col-md-12 col-lg-12 w-xs-full p-0"
              // heading={<IntlMessages id={titleId} />}
              heading={<IntlMessages id="aboutPrice" />}
              collapsible
              fullBlock
              customClasses="overflow-hidden"
            >
              <div className="alert  mt-2 mb-2" role="alert">
                {rentalDetails?.rentalDetails?.installments?.length &&
                rentalDetails?.rentalDetails?.rentalDateExtensionRequests?.find(
                  (i) => i.status === "confirmed",
                ) ? (
                  <BookingInstallmentPriceSummary
                    isHandover={
                      rentalDetails?.rentalDetails.branchId !==
                        rentalDetails?.rentalDetails.dropOffBranchId ||
                      rentalDetails?.rentalDetails.deliverType === "two_ways"
                    }
                    BookingPrice={BookingInstallmentSumary}
                    calculatingPrice={calculatingPrice}
                    insurance={rentalDetails?.rentalDetails?.insuranceId}
                    BookingDetails={rentalDetails}
                    isUnlimited={rentalDetails?.rentalDetails.isUnlimited}
                    inBookingDetails
                    bookingType={rentalDetails?.rentalDetails?.isRentToOwn ? "rent-to-own" : ""}
                    plan={rentalDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan}
                    deliveryType={rentalDetails?.rentalDetails?.deliverType}
                  />
                ) : (
                  <BookingPriceSummary
                    isHandover={
                      rentalDetails?.rentalDetails.branchId !==
                        rentalDetails?.rentalDetails.dropOffBranchId ||
                      rentalDetails?.rentalDetails.deliverType === "two_ways"
                    }
                    BookingPrice={BookingInstallmentSumary}
                    calculatingPrice={calculatingPrice}
                    insurance={rentalDetails?.rentalDetails?.insuranceId}
                    BookingDetails={rentalDetails}
                    isUnlimited={rentalDetails?.rentalDetails.isUnlimited}
                    inBookingDetails
                    bookingType={rentalDetails?.rentalDetails?.isRentToOwn ? "rent-to-own" : ""}
                    plan={rentalDetails?.rentalDetails?.ownCarDetails?.rentalOwnCarPlan}
                    deliveryType={rentalDetails?.rentalDetails?.deliverType}
                  />
                )}
              </div>
            </RctCollapsibleCard>
          </div>

          {rentalDetails?.rentalDetails?.integratedRentalContracts?.length
            ? rentalDetails?.rentalDetails?.integratedRentalContracts?.map(
                (RentalContract, index) => (
                  <InfoCard
                    key={`rental-contract-${RentalContract.id || index}`}
                    fullwidth
                    RentalContract={RentalContract}
                    titleId="Integrated Ally's Contract"
                    inbookingDetails
                  />
                ),
              )
            : null}
          <div className="booking-details-card">
            <InfoCard
              fullwidth
              data={rentalDetails?.rentalDetails?.notes}
              titleId="rental.notes"
              inbookingDetails
              notes
            />
          </div>

          {rentalDetails?.rentalDetails?.deliverLng && (
            <div className="booking-details-card">
              <GoogleMapComponent
                heading={formatMessage({ id: "deliveryLocation" })}
                lat={rentalDetails?.rentalDetails?.deliverLat}
                lng={rentalDetails?.rentalDetails?.deliverLng}
              />
            </div>
          )}
        </div>
      </div>

      <div className="row mt-4">
        {rentalDetails?.rentalDetails?.installments &&
        rentalDetails?.rentalDetails?.installments?.length ? (
          <div className="col-12">
            <div className="booking-details-card">
              <RctCollapsibleCard
                colClasses="col-sm-12 col-md-12 col-lg-12 w-xs-full p-0"
                heading={<IntlMessages id="Rental Installments" />}
                collapsible
                fullBlock
                InInstallment
              >
                {rentalDetails?.rentalDetails?.installments &&
                rentalDetails?.rentalDetails?.installments.length ? (
                  <div>
                    <InstallmentsTable
                      rentalId={rentalDetails?.rentalDetails?.id}
                      Installments={rentalDetails?.rentalDetails?.mergedInstallments}
                      refetchBooking={refetch}
                      bookDetails={rentalDetails?.rentalDetails}
                    />
                  </div>
                ) : null}
              </RctCollapsibleCard>
            </div>
          </div>
        ) : null}

        {openNoteModal && (
          <AddNoteModal
            openNoteModal={openNoteModal}
            setOpenNoteModal={setopenNoteModal}
            rentalId={rentalDetails?.rentalDetails?.id}
            refetch={refetch}
          />
        )}
        {openPriceModal && (
          <UpdateSuggestedPrice
            rentalId={rentalDetails?.rentalDetails?.id}
            openPriceModal={openPriceModal}
            setOpenPriceModal={setOpenPriceModal}
          />
        )}
        {openStatusModal && (
          <ChangeStatusModal
            openModal={openStatusModal}
            setOpenModal={setOpenStatusModal}
            inBookingDetails
            rentalDetails={rentalDetails}
            rentalDateExtensionRequests={rentalDetails?.rentalDetails?.rentalDateExtensionRequests}
          />
        )}
        {openDuration && (
          <ChangeDuration
            rentalDetails={rentalDetails?.rentalDetails}
            openDuration={openDuration}
            setOpenDuration={setOpenDuration}
            refetch={refetch}
          />
        )}
        {openextraService && (
          <UpdateExtraService
            rentalDetails={rentalDetails?.rentalDetails}
            openextraService={openextraService}
            setOpenExtraService={setOpenExtraService}
            refetch={refetch}
            carDetailsRes={carDetailsRes}
          />
        )}
      </div>
      {rentalDetails?.rentalDetails?.id && !user?.user_type?.includes("agency") && (
        <div className="d-flex flex-row-reverse m-4">
          {daysDifference(pickUpDate, dropOffDate, status) !== "STOP_EDIT" &&
            status !== "car_received" && (
              <button
                type="button"
                className="btn btn-primary mr-1 ml-1"
                onClick={() =>
                  history.push(`/cw/dashboard/bookings/${rentalDetails?.rentalDetails?.id}/edit`)
                }
              >
                <FormattedMessage id="common.editSomething" values={{ something: "booking" }} />
              </button>
            )}
          {daysDifference(pickUpDate, dropOffDate, status) !== "STOP_EDIT" && (
            <button
              type="button"
              className="btn btn-primary mr-1 ml-1"
              onClick={() =>
                history.push(`/cw/dashboard/changeStatus/${rentalDetails?.rentalDetails?.id}`)
              }
            >
              <FormattedMessage id="changeStatus" />
            </button>
          )}
        </div>
      )}
      {progress && (
        <div
          className="d-flex justify-content-center align-items-center"
          style={{
            zIndex: "999",
            position: "fixed",
            top: 0,
            width: "calc(100vw - 140px)",
            height: "100vh",
            left: locale === "ar" ? 0 : "",
            right: locale === "ar" ? "" : 0,
          }}
        >
          <CircularProgress />
        </div>
      )}
      {extensionModalOpen && (
        <ExtenstionRequests
          extensionModalOpen={extensionModalOpen}
          setIsExtensionModalOpen={setIsExtensionModalOpen}
          rentalDetails={rentalDetails?.rentalDetails}
          rentalDateExtensionRequests={rentalDetails?.rentalDetails?.rentalDateExtensionRequests}
          hasPendingExtensionRequests={rentalDetails?.rentalDetails?.hasPendingExtensionRequests}
          refetchBooking={refetch}
        />
      )}
      {Boolean(userCan("rentals.timeline")) && (
        <TimeLine
          isOpen={opneTimeLineModal}
          setOpenTimeLineModal={setOpenTimeLineModal}
          BookingId={bookingId}
        />
      )}
      <UsersModal
        isOpen={OpenUsersModal}
        setOpenUsersModal={setOpenUsersModal}
        customerCare={customerCare}
        BookingDetails={rentalDetails?.rentalDetails}
        AssignBookingBySuperUser={AssignBookingBySuperUser}
      />
    </div>
  );
}
