/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable eqeqeq */
/* eslint-disable no-nested-ternary */
/** Bookings List */
import React, { useEffect, useState } from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Link, useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { Typography, Tooltip } from "@material-ui/core";
import { Pagination } from "@material-ui/lab";
import EditIcon from "@material-ui/icons/Edit";

// import useSetState from "hooks/useSetState";
import CustomTable from "components/shared/CustomTable";
import PerPage from "components/shared/PerPage";
import { userCan } from "functions/userCan";
import { daysDifference } from "functions";

import { useQuery, useMutation } from "@apollo/client";

import { PrintRental } from "gql/mutations/PrintRental.gql";
import Print from "@material-ui/icons/Print";
import print from "print-js";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import { Profile } from "gql/queries/AdminProfile.gql";
import { Autorenew, Cached, History, NewReleases } from "@material-ui/icons";
import TotalResults from "Components/shared/TotalResults";
import TimeLine from "./TimeLineModal";
import store from "../../../../store";
import { bookingTableData, bookingTableDataForAgency } from "./BookingTableData";
import UsersModal from "./UsersModal";
import ExtendModal from "./ExtendModal";
import useAssignBooking from "../hooks/useAssignBooking";
import RefundBooking from "./RefundBooking";
import Refund from "./Refund";

const { ally_id } = store.getState()?.authUser.user;

function BookingsList({
  bookingsRes,
  loading,
  setPage,
  limit,
  setLimit,
  users,
  refetch,
  setOrderBy,
  setSortBy,
  agency,
}) {
  const history = useHistory();
  const { formatMessage } = useIntl();

  const [bookingsState, setBookingsState] = useState({
    collection: [],
    metadata: {},
  });

  const [printrental] = useMutation(PrintRental);

  const { collection, metadata } = bookingsState;
  const [opneTimeLineModal, setOpenTimeLineModal] = useState(false);
  const [BookingId, setBookingId] = useState();
  const [openmodel, setOpenModel] = useState(false);
  const [printProgress, setPrintProgress] = useState(false);
  const { data: userInfo } = useQuery(Profile);
  const {
    AssignBooking,
    BookingRecord,
    OpenUsersModal,
    setOpenUsersModal,
    AssignBookingBySuperUser,
    customerCare,
  } = useAssignBooking({
    refetchBooking: refetch,
  });

  useEffect(() => {
    setBookingsState({
      collection: bookingsRes?.dashboardRentals?.collection,
      metadata: bookingsRes?.dashboardRentals?.metadata,
    });
  }, [bookingsRes]);

  const actions = ({
    id,
    dropOffDate,
    pickUpDate,
    status,
    lastRentalDateExtensionRequest,
    installments,
    isPaid,
  }) => {
    const [DOdate, PUdate] = [dropOffDate, pickUpDate];

    const getRentalAudits = (id) => {
      setBookingId(id);
      setOpenTimeLineModal(true);
    };
    const handelPrint = (id) => {
      setPrintProgress(true);
      printrental({
        variables: {
          rentalId: id,
        },
      }).then((data) => {
        setPrintProgress(false);
        print({ printable: data.data.printRental.fileBase64, type: "pdf", base64: true });
      });
    };
    const getRentalExtend = (id) => {
      setBookingId(id);
      setOpenModel(true);
    };
    return (
      <div className="d-flex" style={{ gap: "5px" }}>
        <>
          {/* CAN EXTEND */}
          {daysDifference(PUdate, DOdate, status) === "IN_BETWEEN" && userCan("rentals.update") && (
            <>
              <Tooltip title={formatMessage({ id: "extend" })} placement="top">
                <Link to={`bookings/${id}/extend`}>
                  <Cached style={{ fontSize: "18px", color: "#5d89d8" }} />
                </Link>
              </Tooltip>
            </>
          )}
          {/* CAN CHANGE STATUS */}
          {daysDifference(PUdate, DOdate, status) !== "STOP_EDIT" && userCan("rentals.update") && (
            <Tooltip title={formatMessage({ id: "changeStatus" })} placement="top">
              <Link to={`changestatus/${id}`}>
                <NewReleases style={{ fontSize: "18px", color: "#5d89d8" }} />
              </Link>
            </Tooltip>
          )}
          {/* CAN EDIT */}
          {/* THIS Part of code is the correct one and it will be applied after an approval from the client */}
          {/* {daysDifference(PUdate, DOdate, status) !== "STOP_EDIT" &&
              status !== "car_received" && (
                // change fa icons
                // <Tooltip title={formatMessage({ id: "common.edit" })} placement="top">
                //   <Link to={`bookings/${id}/edit`}>
                //     <i className="fa fa-edit mr-1 ml-1"></i>
                //   </Link>
                // </Tooltip>
                <></>
              )} */}
          {(status == "pending" || status == "confirmed") &&
            userCan("rentals.update") &&
            !agency &&
            !ally_id &&
            !installments?.length && (
              <Tooltip title={formatMessage({ id: "common.edit" })} placement="top">
                <Link to={`bookings/${id}/edit`}>
                  <EditIcon style={{ fontSize: "18px", color: "#5d89d8" }} />
                </Link>
              </Tooltip>
            )}

          {userCan("rentals.print") && (
            <Tooltip title={formatMessage({ id: "common.print" })} placement="top">
              <Print
                style={{ fontSize: "18px", color: "#5d89d8" }}
                onClick={() => handelPrint(id)}
              />
              {/* karem */}
            </Tooltip>
          )}

          {((userCan("rentals.timeline") && !userInfo?.profile?.allyProfile?.allyId) ||
            agency?.isActive) && (
            <Tooltip title={formatMessage({ id: "common.timeline" })} placement="top">
              <Link>
                <History
                  style={{ fontSize: "18px", color: "#5d89d8" }}
                  onClick={() => getRentalAudits(id)}
                />
              </Link>
            </Tooltip>
          )}
        </>

        {lastRentalDateExtensionRequest && userCan("rentals.extend") && (
          <Tooltip title={formatMessage({ id: "extend" })}>
            <Autorenew
              onClick={() => getRentalExtend(id)}
              style={{ color: "#5d89d8", cursor: "pointer", fontSize: "16px" }}
            />
          </Tooltip>
        )}

        {/* <div>
          <ExtendModal setOpenModel={setOpenModel} openmodel={openmodel} />
        </div> */}
      </div>
    );
  };

  function recallHandler(record) {
    if (ally_id) return;
    if (showRecall(record)) {
      if (!record?.installments) {
        _recallPaymentGateway({ rentalId: record.id }).finally(() => {
          refetch();
        });
      } else {
        _recallPaymentGateway({
          rentalId: record.id,
          installmentId: record?.installments?.find(
            (i) => i?.paymentStatus === "failed" || i?.hasPendingPaymentTransaction,
          )?.id,
        }).finally(() => {
          refetch();
        });
      }
    }
  }

  const showRecall = (record) => {
    if (
      record?.hasPendingPaymentTransaction ||
      record?.installments?.find(
        (i) =>
          i?.paymentStatus === "failed" ||
          i?.hasPendingPaymentTransaction ||
          i?.paymentStatus === "pending",
      ) ||
      record.rentalPayments?.find(
        (i) =>
          i?.status === "failed" || i?.status === "pending" || i?.status == "pending_transaction",
      )
    ) {
      return true;
    }
    return false;
  };

  function NotPayed({ record }) {
    return (
      <div
        style={
          !ally_id && showRecall(record) ? { cursor: "pointer", textDecoration: "underline" } : {}
        }
        onClick={() => {
          recallHandler(record);
        }}
      >
        <div
          title={!ally_id && showRecall(record) ? formatMessage({ id: "RecallGateway" }) : ""}
          style={{ display: "flex", alignItems: "end", gap: "4px", cursor: "pointer" }}
        >
          {showRecall(record) ? <i className="fa fa-refresh" /> : null}
          <p style={{ fontSize: "13px" }}>{formatMessage({ id: "notpayed" })} </p>
        </div>
      </div>
    );
  }
  const refund = (record) =>
    userCan("rentals.refund") && record.refundable ? (
      <div style={{ textAlign: "center", display: "flex", gap: "7px", alignItems: "center" }}>
        <p style={{ fontSize: "13px" }}>
          {" "}
          <FormattedMessage id={record.paymentMethod} />
        </p>
        {record.isPaid && record.paymentMethod == "ONLINE" ? (
          <>
            <p style={{ fontSize: "12px" }} className="badge badge-info m-0">
              <FormattedMessage id="payed" />
            </p>
          </>
        ) : !record.isPaid &&
          record.paymentMethod == "ONLINE" &&
          !record.paymentMethod == "CASH" ? (
          <NotPayed record={record} />
        ) : null}
        {!ally_id && (
          <Refund
            rentalid={record.id}
            is24Passed={record.is24Passed}
            refetch={refetch}
            bookingDetails={record}
          />
        )}
      </div>
    ) : (
      <div>
        <p style={{ fontSize: "13px" }}>
          {" "}
          <FormattedMessage id={record?.paymentMethod} />{" "}
        </p>
        {record.withInstallment || record?.isRentToOwn ? (
          <div style={{ display: "flex", gap: "5px" }}>
            <div
              title={!ally_id && showRecall(record) ? formatMessage({ id: "RecallGateway" }) : ""}
              style={{ display: "flex", alignItems: "end", gap: "4px", cursor: "pointer" }}
              onClick={() => recallHandler(record)}
            >
              {showRecall(record) ? <i className="fa fa-refresh" /> : null}
              {showRecall(record) ? (
                record.installments.filter((item) => item.hasPendingPaymentTransaction)?.length ? (
                  <p className="badge badge-warning ">
                    <FormattedMessage id="pending" />
                  </p>
                ) : (
                  <FormattedMessage id="notpayed" />
                )
              ) : (
                <FormattedMessage id="rent.payed" />
              )}

              {record.installments?.filter(
                (item) => item.status == "partially_refunded" || item.status == "fully_refunded",
              )?.length ? (
                <p style={{ fontSize: "12px" }} className="badge badge-warning m-0">
                  <FormattedMessage id="partially_refunded" />
                </p>
              ) : null}
            </div>
            {record.paidInstallmentsCount}
          </div>
        ) : record.paymentMethod == "ONLINE" && record.hasPendingPaymentTransaction ? (
          <>
            {showRecall(record) ? (
              <i
                className="fa fa-refresh"
                style={{ cursor: "pointer" }}
                onClick={() => recallHandler(record)}
              />
            ) : null}{" "}
            <p className="badge badge-warning ">
              <FormattedMessage id="pending" />
            </p>
          </>
        ) : record.isPaid && record.paymentMethod == "ONLINE" ? (
          <>
            {!record.refundedAt && (
              <p className="badge badge-success" style={{ fontSize: "12px" }}>
                <FormattedMessage id="payed" />
              </p>
            )}
            {record.refundedAt && (
              <p className="badge badge-danger" style={{ fontSize: "12px" }}>
                <FormattedMessage id="refund.money" />
              </p>
            )}
          </>
        ) : !record.isPaid && record.paymentMethod == "ONLINE" ? (
          <NotPayed record={record} />
        ) : null}
      </div>
    );
  return (
    <Typography component="div" style={{ padding: 10, position: "relative" }}>
      <div>
        {/* {
          printProgress && 
            <div className="progress-print" style={{width:"calc(100vw - 8.1rem)",height:"100vh",position:"absolite", display: "flex", justifyContent: "center", alignItems:"center", zIndex:"99", top:"45vh", left:"0"}}>
            <CircularProgress/>

            </div>

          } */}
        <UsersModal
          isOpen={OpenUsersModal}
          setOpenUsersModal={setOpenUsersModal}
          BookingDetails={BookingRecord}
          AssignBookingBySuperUser={AssignBookingBySuperUser}
          customerCare={customerCare}
        />
        <TimeLine
          isOpen={opneTimeLineModal}
          setOpenTimeLineModal={setOpenTimeLineModal}
          BookingId={BookingId}
        />
        <ExtendModal
          setOpenModel={setOpenModel}
          openmodel={openmodel}
          BookingId={BookingId}
          refetch={refetch}
        />
        <RctCollapsibleCard fullBlock table>
          <CustomTable
            tableData={!agency ? bookingTableData : bookingTableDataForAgency}
            loading={printProgress || loading}
            tableRecords={collection}
            actions={actions}
            AssignBooking={AssignBooking}
            RefundBooking={RefundBooking}
            refetch={refetch}
            actionsArgs={[
              "id",
              "pickUpDate",
              "dropOffDate",
              "status",
              "pickUpTime",
              "dropOffTime",
              "lastRentalDateExtensionRequest",
              "installments",
              "mergedInstallments",
              "isPaid",
            ]}
            setOrderBy={setOrderBy}
            setSortBy={setSortBy}
          />
        </RctCollapsibleCard>
      </div>
      <div className="d-flex justify-content-around align-items-center">
        {metadata?.currentPage && (
          <>
            <TotalResults totalCount={metadata?.totalCount} />
            <Pagination
              showFirstButton
              showLastButton
              count={Math.ceil(metadata?.totalCount / limit)}
              page={metadata?.currentPage}
              onChange={(e, value) => {
                setPage(value);
                history.replace({ hash: `page=${value}`, search: history?.location?.search });
              }}
            />
            <PerPage
              handlePerPageChange={(value) => {
                setLimit(value);
                setPage(1);
                history.replace({ hash: `page=1`, search: history?.location?.search });
              }}
              perPage={limit}
              setPage={setPage}
            />
          </>
        )}
      </div>
    </Typography>
  );
}

BookingsList.propTypes = {
  setPage: PropTypes.func,
  setLimit: PropTypes.func,
  loading: PropTypes.bool,
  bookingsRes: PropTypes.object,
  limit: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default BookingsList;
